<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR智能识别系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-camera-retro text-2xl text-blue-600 mr-2"></i>
                    <span class="text-xl font-bold text-gray-800">OCR智能识别</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-cog"></i>
                        <span class="ml-1">设置</span>
                    </a>
                    <a href="#" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-user"></i>
                        <span class="ml-1">个人中心</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 上传区域 -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                <i class="fas fa-cloud-upload-alt text-4xl text-blue-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-700 mb-2">拖拽文件到此处或点击上传</h3>
                <p class="text-sm text-gray-500 mb-4">支持图片、PDF等格式</p>
                <button class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    选择文件
                </button>
            </div>
        </div>

        <!-- 功能模块区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- 批量处理 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-tasks text-2xl text-blue-600 mr-3"></i>
                    <h2 class="text-xl font-semibold text-gray-800">批量处理</h2>
                </div>
                <p class="text-gray-600 mb-4">支持多文件批量上传和处理，提高工作效率</p>
                <a href="batch.html" class="text-blue-600 hover:text-blue-800">
                    开始使用 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <!-- 历史记录 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-history text-2xl text-green-600 mr-3"></i>
                    <h2 class="text-xl font-semibold text-gray-800">历史记录</h2>
                </div>
                <p class="text-gray-600 mb-4">查看和管理历史识别记录，支持导出和分享</p>
                <a href="history.html" class="text-green-600 hover:text-green-800">
                    查看记录 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <!-- 模板管理 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-file-alt text-2xl text-purple-600 mr-3"></i>
                    <h2 class="text-xl font-semibold text-gray-800">模板管理</h2>
                </div>
                <p class="text-gray-600 mb-4">自定义和管理识别模板，提高识别准确率</p>
                <a href="templates.html" class="text-purple-600 hover:text-purple-800">
                    管理模板 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <!-- 系统设置 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-cog text-2xl text-gray-600 mr-3"></i>
                    <h2 class="text-xl font-semibold text-gray-800">系统设置</h2>
                </div>
                <p class="text-gray-600 mb-4">配置AI模型、导出选项等系统参数</p>
                <a href="settings.html" class="text-gray-600 hover:text-gray-800">
                    系统设置 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <p class="text-center text-gray-500 text-sm">
                © 2024 OCR智能识别系统. All rights reserved.
            </p>
        </div>
    </footer>

    <script>
        // 基础交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 文件上传区域交互
            const uploadArea = document.querySelector('.border-dashed');
            const uploadButton = document.querySelector('button');

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-500');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('border-blue-500');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-500');
                // 处理文件上传逻辑
            });

            uploadButton.addEventListener('click', () => {
                // 触发文件选择
            });
        });
    </script>
</body>
</html> 