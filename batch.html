<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量处理 - OCR智能识别系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center">
                        <i class="fas fa-camera-retro text-2xl text-blue-600 mr-2"></i>
                        <span class="text-xl font-bold text-gray-800">OCR智能识别</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="settings.html" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-cog"></i>
                        <span class="ml-1">设置</span>
                    </a>
                    <a href="#" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-user"></i>
                        <span class="ml-1">个人中心</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-2xl font-bold text-gray-800">批量处理</h1>
            <div class="space-x-4">
                <button id="openNewTaskBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    <i class="fas fa-plus mr-2"></i>新建任务
                </button>
                <button class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                    <i class="fas fa-download mr-2"></i>批量导出
                </button>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <select class="border rounded-md px-3 py-1">
                            <option>全部状态</option>
                            <option>处理中</option>
                            <option>已完成</option>
                            <option>失败</option>
                        </select>
                        <input type="text" placeholder="搜索任务..." class="border rounded-md px-3 py-1">
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="text-gray-600 hover:text-blue-600">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="text-gray-600 hover:text-blue-600">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 任务表格 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                任务名称
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                文件数量
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                进度
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                创建时间
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 示例任务行 -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i class="fas fa-file-alt text-blue-500 mr-2"></i>
                                    <span class="text-sm font-medium text-gray-900">发票识别任务</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                10
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    处理中
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2024-03-20 14:30
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openTaskDetailModal()">查看</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <!-- 更多任务行... -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">20</span> 条结果
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                1
                            </button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                2
                            </button>
                            <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新建任务弹窗 -->
        <div id="newTaskModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
          <div class="bg-white rounded-lg shadow-lg w-full max-w-lg p-8">
            <h2 class="text-xl font-bold mb-6">新建批量任务</h2>
            <form>
              <div class="mb-4">
                <label class="block text-gray-700 mb-1">任务名称</label>
                <input type="text" class="w-full border rounded px-3 py-2" placeholder="请输入任务名称">
              </div>
              <div class="mb-4">
                <label class="block text-gray-700 mb-1">选择模板</label>
                <select class="w-full border rounded px-3 py-2">
                  <option>增值税发票模板</option>
                  <option>收据模板</option>
                  <option>表格模板</option>
                </select>
              </div>
              <div class="mb-4">
                <label class="block text-gray-700 mb-1">上传文件</label>
                <div class="flex items-center justify-center w-full">
                  <label for="file-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded cursor-pointer hover:border-blue-400">
                    <i class="fas fa-cloud-upload-alt text-3xl text-blue-500 mb-2"></i>
                    <span class="text-gray-500">点击或拖拽文件到此处</span>
                    <input id="file-upload" type="file" class="hidden" multiple>
                  </label>
                </div>
              </div>
              <div class="mb-4">
                <label class="block text-gray-700 mb-1">识别类型</label>
                <div class="flex space-x-4">
                  <label class="flex items-center"><input type="checkbox" class="mr-2">通用文字</label>
                  <label class="flex items-center"><input type="checkbox" class="mr-2">表格</label>
                  <label class="flex items-center"><input type="checkbox" class="mr-2">版面分析</label>
                </div>
              </div>
              <div class="flex justify-end space-x-2 mt-6">
                <button type="button" class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300" onclick="closeNewTaskModal()">取消</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">创建任务</button>
              </div>
            </form>
          </div>
        </div>

        <!-- 任务详情弹窗 -->
        <div id="taskDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
          <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl p-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-bold">任务详情</h2>
              <button class="text-gray-400 hover:text-gray-600" onclick="closeTaskDetailModal()">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <div class="mb-4">
              <div class="flex flex-wrap gap-6 text-sm text-gray-600">
                <div>任务名称：<span class="font-medium text-gray-800">发票识别任务</span></div>
                <div>模板：<span class="font-medium text-gray-800">增值税发票模板</span></div>
                <div>创建时间：<span class="font-medium text-gray-800">2024-03-20 14:30</span></div>
                <div>状态：<span class="font-medium text-green-600">处理中</span></div>
              </div>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full text-sm text-left">
                <thead>
                  <tr>
                    <th class="py-2 px-4">文件名</th>
                    <th class="py-2 px-4">状态</th>
                    <th class="py-2 px-4">进度</th>
                    <th class="py-2 px-4">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="py-2 px-4">发票1.pdf</td>
                    <td class="py-2 px-4"><span class="bg-green-100 text-green-700 px-2 py-1 rounded">已完成</span></td>
                    <td class="py-2 px-4"><div class="w-24 bg-gray-200 rounded h-2"><div class="bg-blue-500 h-2 rounded" style="width:100%"></div></div></td>
                    <td class="py-2 px-4">
                      <button class="text-blue-600 hover:underline mr-2">查看</button>
                      <button class="text-green-600 hover:underline">导出</button>
                    </td>
                  </tr>
                  <!-- 更多文件行... -->
                </tbody>
              </table>
            </div>
            <div class="flex justify-between items-center mt-6">
              <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">导出全部结果</button>
              <button class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300" onclick="closeTaskDetailModal()">关闭</button>
            </div>
          </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <p class="text-center text-gray-500 text-sm">
                © 2024 OCR智能识别系统. All rights reserved.
            </p>
        </div>
    </footer>

    <script>
        // 基础交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 状态筛选
            const statusSelect = document.querySelector('select');
            statusSelect.addEventListener('change', function() {
                // 处理状态筛选逻辑
            });

            // 搜索功能
            const searchInput = document.querySelector('input[type="text"]');
            searchInput.addEventListener('input', function() {
                // 处理搜索逻辑
            });

            // 新建任务弹窗控制
            const newTaskModal = document.getElementById('newTaskModal');
            const openNewTaskBtn = document.getElementById('openNewTaskBtn');
            openNewTaskBtn.addEventListener('click', function() {
                newTaskModal.classList.remove('hidden');
            });
            window.closeNewTaskModal = function() {
                newTaskModal.classList.add('hidden');
            };

            // 任务详情弹窗控制
            const taskDetailModal = document.getElementById('taskDetailModal');
            window.openTaskDetailModal = function() {
                taskDetailModal.classList.remove('hidden');
            };
            window.closeTaskDetailModal = function() {
                taskDetailModal.classList.add('hidden');
            };
        });
    </script>
</body>
</html> 