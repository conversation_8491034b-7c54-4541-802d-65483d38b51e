<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板管理 - OCR智能识别系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center">
                        <i class="fas fa-camera-retro text-2xl text-blue-600 mr-2"></i>
                        <span class="text-xl font-bold text-gray-800">OCR智能识别</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="settings.html" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-cog"></i>
                        <span class="ml-1">设置</span>
                    </a>
                    <a href="#" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-user"></i>
                        <span class="ml-1">个人中心</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-2xl font-bold text-gray-800">模板管理</h1>
            <div class="space-x-4">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    <i class="fas fa-plus mr-2"></i>新建模板
                </button>
                <button class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                    <i class="fas fa-upload mr-2"></i>导入模板
                </button>
            </div>
        </div>

        <!-- 模板分类 -->
        <div class="mb-8">
            <div class="flex space-x-4">
                <button class="px-4 py-2 rounded-md bg-blue-600 text-white">
                    全部模板
                </button>
                <button class="px-4 py-2 rounded-md bg-white text-gray-700 hover:bg-gray-50">
                    发票模板
                </button>
                <button class="px-4 py-2 rounded-md bg-white text-gray-700 hover:bg-gray-50">
                    收据模板
                </button>
                <button class="px-4 py-2 rounded-md bg-white text-gray-700 hover:bg-gray-50">
                    表格模板
                </button>
            </div>
        </div>

        <!-- 模板列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 模板卡片 -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-file-invoice text-2xl text-blue-500 mr-3"></i>
                            <h3 class="text-lg font-semibold text-gray-800">增值税发票模板</h3>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-600 hover:text-blue-600">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-600 hover:text-red-600">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        适用于增值税专用发票、普通发票等票据识别
                    </p>
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>创建时间：2024-03-20</span>
                        <span>使用次数：128</span>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3">
                    <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        使用模板
                    </button>
                </div>
            </div>

            <!-- 更多模板卡片 -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-receipt text-2xl text-green-500 mr-3"></i>
                            <h3 class="text-lg font-semibold text-gray-800">收据模板</h3>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-600 hover:text-blue-600">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-600 hover:text-red-600">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        适用于各类收据、小票等票据识别
                    </p>
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>创建时间：2024-03-19</span>
                        <span>使用次数：85</span>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3">
                    <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        使用模板
                    </button>
                </div>
            </div>

            <!-- 新建模板卡片 -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden border-2 border-dashed border-gray-300">
                <div class="p-4 flex flex-col items-center justify-center h-full">
                    <i class="fas fa-plus-circle text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-600 mb-2">新建模板</h3>
                    <p class="text-gray-500 text-sm text-center mb-4">
                        创建新的识别模板，自定义识别规则
                    </p>
                    <button class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                        开始创建
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <p class="text-center text-gray-500 text-sm">
                © 2024 OCR智能识别系统. All rights reserved.
            </p>
        </div>
    </footer>

    <script>
        // 基础交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 模板分类切换
            const categoryButtons = document.querySelectorAll('.flex.space-x-4 button');
            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    categoryButtons.forEach(btn => {
                        btn.classList.remove('bg-blue-600', 'text-white');
                        btn.classList.add('bg-white', 'text-gray-700');
                    });
                    this.classList.remove('bg-white', 'text-gray-700');
                    this.classList.add('bg-blue-600', 'text-white');
                });
            });
        });
    </script>
</body>
</html> 