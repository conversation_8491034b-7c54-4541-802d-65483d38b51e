# OCR智能处理系统

基于PaddleOCR的智能文档处理系统，支持多场景OCR识别、表格提取、数据导出等功能，并集成了大模型能力进行智能处理。

## 功能特点

### 核心功能
- 多场景OCR识别
  - 通用文字识别
  - 表格识别
  - 版面分析
  - 多语言支持
- 智能表格处理
  - 表格结构识别
  - 数据提取
  - 表格分类
  - 数据验证
- 数据导出
  - Excel/CSV导出
  - 自定义模板
  - 批量导出
  - 数据格式化

### 扩展功能
- 大模型增强
  - 本地模型支持
    - Llama
    - ChatGLM
    - 通义千问
  - 在线模型支持
    - OpenAI
    - 百度文心
    - 阿里通义
  - 智能处理
    - OCR结果优化
    - 文本纠错
    - 数据补全
    - 智能分类

## 系统架构

### 前端架构
```
frontend/
├── components/           # 可复用组件
├── pages/               # 页面组件
├── services/           # API服务
├── utils/             # 工具函数
└── store/             # 状态管理
```

### 后端架构
```
backend/
├── api/                # API路由层
├── core/              # 核心业务逻辑
├── models/            # 数据模型
├── services/          # 服务层
└── utils/             # 工具函数
```

### AI模块架构
```
ai/
├── adapters/           # 模型适配器
├── config/            # 模型配置
└── utils/            # 工具函数
```

## 技术栈

### 前端
- React/Vue
- TypeScript
- Ant Design/Element Plus
- Axios
- Redux/Vuex

### 后端
- Python
- FastAPI/Flask
- PaddleOCR
- SQLAlchemy
- Redis

### AI
- PaddlePaddle
- Transformers
- LangChain
- OpenAI API
- 百度文心API

## 开发计划

### 第一阶段：基础功能
- [x] 项目初始化
- [ ] 基础OCR功能
- [ ] 表格识别
- [ ] 数据导出

### 第二阶段：AI增强
- [ ] 大模型集成
- [ ] 智能处理
- [ ] 结果优化

### 第三阶段：功能完善
- [ ] 批量处理
- [ ] 模板管理
- [ ] 用户系统

## 安装部署

### 环境要求
- Python 3.8+
- Node.js 16+
- CUDA 11.0+ (GPU支持)

### 安装步骤
1. 克隆项目
```bash
git clone https://github.com/your-username/ocr-system.git
```

2. 安装依赖
```bash
# 后端依赖
pip install -r requirements.txt

# 前端依赖
cd frontend
npm install
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件配置相关参数
```

4. 启动服务
```bash
# 启动后端
python run.py

# 启动前端
cd frontend
npm run dev
```

## 使用说明

### 基本使用
1. 上传图片
2. 选择识别类型
3. 等待处理结果
4. 编辑/导出数据

### 高级功能
1. 配置大模型
2. 自定义模板
3. 批量处理

## 贡献指南
欢迎提交Issue和Pull Request

## 许可证
MIT License 