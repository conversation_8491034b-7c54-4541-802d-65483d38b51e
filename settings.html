<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - OCR智能识别系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center">
                        <i class="fas fa-camera-retro text-2xl text-blue-600 mr-2"></i>
                        <span class="text-xl font-bold text-gray-800">OCR智能识别</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="settings.html" class="text-blue-600">
                        <i class="fas fa-cog"></i>
                        <span class="ml-1">设置</span>
                    </a>
                    <a href="#" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-user"></i>
                        <span class="ml-1">个人中心</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-gray-800">系统设置</h1>
        </div>

        <!-- 设置内容 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- 设置导航 -->
            <div class="border-b">
                <nav class="flex">
                    <button class="px-6 py-4 text-blue-600 border-b-2 border-blue-600">
                        AI模型设置
                    </button>
                    <button class="px-6 py-4 text-gray-600 hover:text-blue-600">
                        导出设置
                    </button>
                    <button class="px-6 py-4 text-gray-600 hover:text-blue-600">
                        系统设置
                    </button>
                </nav>
            </div>

            <!-- AI模型设置 -->
            <div class="p-6">
                <!-- 本地模型设置 -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">本地模型设置</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700">模型选择</h3>
                                <p class="text-sm text-gray-500">选择要使用的本地模型</p>
                            </div>
                            <select class="border rounded-md px-3 py-2 w-64">
                                <option>Llama 2</option>
                                <option>ChatGLM</option>
                                <option>通义千问</option>
                            </select>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700">模型路径</h3>
                                <p class="text-sm text-gray-500">本地模型文件路径</p>
                            </div>
                            <div class="flex space-x-2 w-64">
                                <input type="text" class="border rounded-md px-3 py-2 flex-1" placeholder="请输入模型路径">
                                <button class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200">
                                    浏览
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700">设备选择</h3>
                                <p class="text-sm text-gray-500">选择运行模型的设备</p>
                            </div>
                            <select class="border rounded-md px-3 py-2 w-64">
                                <option>CPU</option>
                                <option>GPU</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 在线模型设置 -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">在线模型设置</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700">模型选择</h3>
                                <p class="text-sm text-gray-500">选择要使用的在线模型</p>
                            </div>
                            <select class="border rounded-md px-3 py-2 w-64">
                                <option>OpenAI GPT-4</option>
                                <option>百度文心</option>
                                <option>阿里通义</option>
                            </select>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700">API密钥</h3>
                                <p class="text-sm text-gray-500">输入模型API密钥</p>
                            </div>
                            <div class="flex space-x-2 w-64">
                                <input type="password" class="border rounded-md px-3 py-2 flex-1" placeholder="请输入API密钥">
                                <button class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200">
                                    测试
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模型参数设置 -->
                <div>
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">模型参数设置</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700">最大长度</h3>
                                <p class="text-sm text-gray-500">模型输出最大长度</p>
                            </div>
                            <input type="number" class="border rounded-md px-3 py-2 w-64" value="2048">
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700">温度</h3>
                                <p class="text-sm text-gray-500">模型输出随机性</p>
                            </div>
                            <input type="range" class="w-64" min="0" max="1" step="0.1" value="0.7">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 保存按钮 -->
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    保存设置
                </button>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <p class="text-center text-gray-500 text-sm">
                © 2024 OCR智能识别系统. All rights reserved.
            </p>
        </div>
    </footer>

    <script>
        // 基础交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 设置导航切换
            const navButtons = document.querySelectorAll('nav button');
            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    navButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                        btn.classList.add('text-gray-600');
                    });
                    this.classList.remove('text-gray-600');
                    this.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
                });
            });

            // 温度滑块显示值
            const temperatureInput = document.querySelector('input[type="range"]');
            temperatureInput.addEventListener('input', function() {
                // 可以添加显示当前值的逻辑
            });
        });
    </script>
</body>
</html> 